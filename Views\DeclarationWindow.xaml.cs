using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for DeclarationWindow.xaml
    /// </summary>
    public partial class DeclarationWindow : Window
    {
        private readonly FieldVisit _selectedVisit;

        public DeclarationWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        public DeclarationWindow(FieldVisit selectedVisit)
        {
            InitializeComponent();
            _selectedVisit = selectedVisit;
            LoadVisitData();
        }

        private void LoadSampleData()
        {
            // Load sample data for testing
            VisitNumberTextBlock.Text = "911-13169";
        }

        private void LoadVisitData()
        {
            if (_selectedVisit != null)
            {
                VisitNumberTextBlock.Text = _selectedVisit.VisitNumber ?? "---";
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Hide buttons during printing
                var buttonsPanel = FindButtonsPanel();
                if (buttonsPanel != null)
                {
                    buttonsPanel.Visibility = Visibility.Collapsed;
                }

                // Create print dialog
                var printDialog = new System.Windows.Controls.PrintDialog();
                
                if (printDialog.ShowDialog() == true)
                {
                    // Print the entire window content
                    var visual = Content as Visual;
                    if (visual != null)
                    {
                        printDialog.PrintVisual(visual, "بيان وإقرار - تضارب المصالح");
                    }
                }

                // Show buttons again
                if (buttonsPanel != null)
                {
                    buttonsPanel.Visibility = Visibility.Visible;
                }

                MessageBox.Show("تم إرسال المستند للطباعة بنجاح", "طباعة", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Here you can implement save functionality
                // For now, just show a message
                MessageBox.Show("تم حفظ البيان بنجاح", "حفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الحفظ: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private StackPanel FindButtonsPanel()
        {
            // Find the buttons panel to hide during printing
            return FindVisualChild<StackPanel>(this, panel => 
                panel.Children.Count > 0 && 
                panel.Children[0] is Button button && 
                button.Name == "PrintButton");
        }

        private static T FindVisualChild<T>(DependencyObject parent, Func<T, bool> predicate = null) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                
                if (child is T typedChild && (predicate == null || predicate(typedChild)))
                {
                    return typedChild;
                }

                var foundChild = FindVisualChild<T>(child, predicate);
                if (foundChild != null)
                {
                    return foundChild;
                }
            }
            return null;
        }
    }
}
