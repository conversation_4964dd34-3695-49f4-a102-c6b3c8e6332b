<Window x:Class="DriverManagementSystem.Views.DeclarationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="بيان وإقرار - تضارب المصالح والعلاقات العائلية"
        Height="900" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="White">

    <Window.Resources>
        <!-- Official Document Style -->
        <Style x:Key="OfficialHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>

        <Style x:Key="OfficialTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="20"/>
        </Style>

        <Style x:Key="TableHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>

        <Style x:Key="TableCellStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Padding" Value="5"/>
        </Style>

        <Style x:Key="CheckBoxStyle" TargetType="CheckBox">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </Window.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
        <StackPanel>
            <!-- Header Section -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="100"/>
                </Grid.ColumnDefinitions>

                <!-- Visit Number (Keep as is) -->
                <Border Grid.Column="0" BorderBrush="Green" BorderThickness="2" Padding="8,4" Background="LightGreen">
                    <StackPanel>
                        <TextBlock Text="رقم الزيارة:" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Name="VisitNumberTextBlock" Text="911-13169" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Center Header -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="بيان وإقرار" Style="{StaticResource OfficialHeaderStyle}" FontSize="18" Margin="0,0,0,5"/>
                    <TextBlock Text="تضارب المصالح والعلاقات العائلية" Style="{StaticResource OfficialHeaderStyle}" FontSize="14"/>
                </StackPanel>

                <!-- Logo -->
                <Border Grid.Column="2" BorderBrush="Black" BorderThickness="1" Padding="5" Background="LightGray">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="SFD" FontWeight="Bold" FontSize="16" HorizontalAlignment="Center"/>
                        <TextBlock Text="للصندوق" FontSize="8" HorizontalAlignment="Center"/>
                        <TextBlock Text="الاجتماعي" FontSize="8" HorizontalAlignment="Center"/>
                        <TextBlock Text="للتنمية" FontSize="8" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Declaration Title -->
            <Border BorderBrush="Black" BorderThickness="2" Padding="10" Margin="0,0,0,15" Background="White">
                <TextBlock Text="إقرار مزود الخدمة" Style="{StaticResource OfficialHeaderStyle}" FontSize="16"/>
            </Border>

            <!-- Declaration Text -->
            <Border BorderBrush="Black" BorderThickness="1" Padding="15" Margin="0,0,0,15" Background="White">
                <TextBlock Style="{StaticResource OfficialTextStyle}">
                    <Run Text="أقر أنا مزود الخدمة صاحب الشركة رقم "/>
                    <Run Text="9-18093" FontWeight="Bold"/>
                    <Run Text=" بأنني تسلمت من الصندوق الاجتماعي للتنمية نسخة من مدونة تضارب المصالح، وبخصوص تعاملي مع العاملين في الصندوق فإنني أقر بما يلي:"/>
                </TextBlock>
            </Border>

            <!-- Relations Section -->
            <Border BorderBrush="Black" BorderThickness="2" Padding="10" Margin="0,0,0,15" Background="White">
                <TextBlock Text="العلاقات العائلية" Style="{StaticResource OfficialHeaderStyle}" FontSize="14"/>
            </Border>

            <!-- Question 1 -->
            <Border BorderBrush="Black" BorderThickness="1" Padding="15" Margin="0,0,0,10" Background="White">
                <StackPanel>
                    <TextBlock Text="(1) هل لديك علاقة عائلية مع أحد العاملين في الصندوق؟" Style="{StaticResource OfficialTextStyle}" FontWeight="Bold" Margin="0,0,0,10"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                        <CheckBox Content="نعم" Style="{StaticResource CheckBoxStyle}" Margin="20,0"/>
                        <CheckBox Content="لا" Style="{StaticResource CheckBoxStyle}" Margin="20,0"/>
                    </StackPanel>

                    <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" Style="{StaticResource OfficialTextStyle}" HorizontalAlignment="Center" Margin="0,0,0,10"/>

                    <!-- Table for Relations -->
                    <Border BorderBrush="Black" BorderThickness="1" Background="White">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Table Headers -->
                            <Border Grid.Row="0" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1" Background="Black">
                                <TextBlock Text="اسم العامل" Style="{StaticResource TableHeaderStyle}"/>
                            </Border>
                            <Border Grid.Row="0" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1" Background="Black">
                                <TextBlock Text="نوع الصندوق" Style="{StaticResource TableHeaderStyle}"/>
                            </Border>
                            <Border Grid.Row="0" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1" Background="Black">
                                <TextBlock Text="نوع العلاقة العائلية" Style="{StaticResource TableHeaderStyle}"/>
                            </Border>

                            <!-- Table Rows -->
                            <Border Grid.Row="1" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>

                            <Border Grid.Row="2" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="2" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="2" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>

                            <Border Grid.Row="3" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="3" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="3" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,0">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                        </Grid>
                    </Border>
                </StackPanel>
            </Border>

            <!-- Question 2 -->
            <Border BorderBrush="Black" BorderThickness="1" Padding="15" Margin="0,0,0,15" Background="White">
                <StackPanel>
                    <TextBlock Text="(2) هل لديك مصالح مباشرة أو غير مباشرة مع أحد العاملين في الصندوق؟" Style="{StaticResource OfficialTextStyle}" FontWeight="Bold" Margin="0,0,0,10"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                        <CheckBox Content="نعم" Style="{StaticResource CheckBoxStyle}" Margin="20,0"/>
                        <CheckBox Content="لا" Style="{StaticResource CheckBoxStyle}" Margin="20,0"/>
                    </StackPanel>

                    <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" Style="{StaticResource OfficialTextStyle}" HorizontalAlignment="Center" Margin="0,0,0,10"/>

                    <!-- Table for Interests -->
                    <Border BorderBrush="Black" BorderThickness="1" Background="White">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                                <RowDefinition Height="35"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Table Headers -->
                            <Border Grid.Row="0" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1" Background="Black">
                                <TextBlock Text="اسم العامل" Style="{StaticResource TableHeaderStyle}"/>
                            </Border>
                            <Border Grid.Row="0" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1" Background="Black">
                                <TextBlock Text="فرع الصندوق" Style="{StaticResource TableHeaderStyle}"/>
                            </Border>
                            <Border Grid.Row="0" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1" Background="Black">
                                <TextBlock Text="نوع المصلحة" Style="{StaticResource TableHeaderStyle}"/>
                            </Border>

                            <!-- Empty rows for filling -->
                            <Border Grid.Row="1" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="1" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>

                            <Border Grid.Row="2" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="2" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="2" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,1">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>

                            <Border Grid.Row="3" Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="3" Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                            <Border Grid.Row="3" Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,0,0">
                                <TextBox BorderThickness="0" Background="Transparent" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>
                        </Grid>
                    </Border>
                </StackPanel>
            </Border>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button Name="PrintButton" Content="طباعة" Width="100" Height="35" Margin="10,0" 
                        Background="White" BorderBrush="Black" BorderThickness="1" 
                        FontFamily="Arial" FontSize="12" Click="PrintButton_Click"/>
                <Button Name="SaveButton" Content="حفظ" Width="100" Height="35" Margin="10,0"
                        Background="White" BorderBrush="Black" BorderThickness="1"
                        FontFamily="Arial" FontSize="12" Click="SaveButton_Click"/>
                <Button Name="CloseButton" Content="إغلاق" Width="100" Height="35" Margin="10,0"
                        Background="White" BorderBrush="Black" BorderThickness="1"
                        FontFamily="Arial" FontSize="12" Click="CloseButton_Click"/>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</Window>
